#!/usr/bin/env python3
"""
测试阿里云文档解析API
"""

import asyncio
import os
from src.config.settings import AlibabaCloudSettings
from src.document_rag.ali_service import AlibabaCloudService

async def test_aliyun_api():
    """测试阿里云API"""
    
    print("🚀 开始测试阿里云文档解析API...")
    
    # 初始化配置
    settings = AlibabaCloudSettings()
    service = AlibabaCloudService(settings)
    
    # 测试文件URL (使用MinIO中已存在的文件)
    test_file_url = "http://119.3.237.14:9000/zhanshu/documents/X75V2.pdf"
    test_file_name = "X75V2.pdf"
    
    try:
        # 1. 提交解析任务
        print(f"📤 提交解析任务: {test_file_name}")
        submit_result = await service.submit_file(test_file_url, test_file_name)
        
        if not submit_result["success"]:
            print(f"❌ 任务提交失败: {submit_result['error']}")
            return False
        
        job_id = submit_result["job_id"]
        print(f"✅ 任务提交成功，任务ID: {job_id}")
        
        # 2. 检查任务状态
        print(f"🔍 检查任务状态...")

        # 直接测试状态查询API
        from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
        status_request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)

        try:
            status_response = await asyncio.to_thread(service.client.query_doc_parser_status, status_request)
            print(f"📊 状态响应类型: {type(status_response)}")

            if status_response and status_response.body:
                print(f"📊 Body类型: {type(status_response.body)}")
                print(f"📊 Body属性: {dir(status_response.body)}")

                if hasattr(status_response.body, 'data'):
                    data = status_response.body.data
                    print(f"📊 Data类型: {type(data)}")
                    if isinstance(data, dict):
                        print(f"📊 Data键: {list(data.keys())}")
                        status = data.get('status', 'unknown')
                    else:
                        print(f"📊 Data属性: {dir(data)}")
                        status = getattr(data, 'status', 'unknown')

                    print(f"✅ 任务状态: {status}")

                    if status.lower() == "success":
                        print(f"✅ 解析完成!")

                        # 3. 获取解析结果
                    print(f"📥 获取解析结果...")

                    # 直接调用阿里云API获取原始响应
                    from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
                    request = docmind_api20220711_models.GetDocParserResultRequest(
                        id=job_id,
                        layout_num=0,
                        layout_step_size=100
                    )

                    response = await asyncio.to_thread(service.client.get_doc_parser_result, request)

                    if response and response.body and response.body.data:
                        data = response.body.data
                        print(f"📊 原始响应数据类型: {type(data)}")

                        # 如果是字典类型，直接访问键
                        if isinstance(data, dict):
                            print(f"📊 数据键: {list(data.keys())}")

                            if 'layouts' in data:
                                layouts = data['layouts']
                                print(f"📊 Layouts数量: {len(layouts) if layouts else 0}")

                                if layouts:
                                    markdown_content = ""
                                    for i, layout in enumerate(layouts[:3]):  # 只显示前3个
                                        print(f"  Layout {i}: {type(layout)}")
                                        if isinstance(layout, dict):
                                            print(f"    键: {list(layout.keys())}")
                                            if 'markdownContent' in layout:
                                                content = layout['markdownContent']
                                                print(f"    Markdown: {content[:100] if content else 'None'}")
                                                if content:
                                                    markdown_content += content + "\n"

                                    print(f"✅ 解析成功!")
                                    print(f"📄 总Markdown内容长度: {len(markdown_content)} 字符")
                                    print(f"📄 前200字符预览:")
                                    print(markdown_content[:200] + "..." if len(markdown_content) > 200 else markdown_content)
                                    return True
                                else:
                                    print(f"❌ 没有找到layouts数据")
                                    return False
                            else:
                                print(f"❌ 响应中没有layouts字段")
                                return False
                        else:
                            # 对象类型处理
                            print(f"📊 数据属性: {dir(data)}")

                            if hasattr(data, 'layouts'):
                                print(f"📊 Layouts数量: {len(data.layouts) if data.layouts else 0}")
                                if data.layouts:
                                    for i, layout in enumerate(data.layouts[:3]):  # 只显示前3个
                                        print(f"  Layout {i}: {type(layout)}")
                                        print(f"    属性: {dir(layout)}")
                                        if hasattr(layout, 'markdown_content'):
                                            print(f"    Markdown: {layout.markdown_content[:100] if layout.markdown_content else 'None'}")

                            # 使用我们的服务方法
                            result = await service.get_job_result(job_id)

                            if result["success"]:
                                markdown_content = result["result"]
                                print(f"✅ 获取结果成功!")
                                print(f"📄 Markdown内容长度: {len(markdown_content)} 字符")
                                print(f"📄 前200字符预览:")
                                print(markdown_content[:200] + "..." if len(markdown_content) > 200 else markdown_content)
                                return True
                            else:
                                print(f"❌ 获取结果失败: {result['error']}")
                                return False
                    else:
                        print(f"❌ 阿里云API返回空响应")
                        return False
                else:
                    print(f"❌ 解析失败")
                    return False
            
            # 等待10秒后重试
            await asyncio.sleep(10)
        
        print(f"❌ 解析超时")
        return False
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_aliyun_api())
    
    if success:
        print("\n🎉 阿里云API测试成功!")
    else:
        print("\n💥 阿里云API测试失败!")
