#!/usr/bin/env python3
"""
测试文档上传接口
"""

import requests
import json
import os

def test_document_upload():
    """测试文档上传功能"""
    
    # API端点
    url = "http://127.0.0.1:8000/api/v1/documents/upload"
    
    # 测试文件路径
    test_file_path = "src/docs/测试文档/ZERO75.pdf"
    
    if not os.path.exists(test_file_path):
        print(f"❌ 测试文件不存在: {test_file_path}")
        return False
    
    try:
        # 准备文件
        with open(test_file_path, 'rb') as f:
            files = {'files': (os.path.basename(test_file_path), f, 'application/pdf')}
            data = {'batch_name': '测试批次'}
            
            print(f"📤 上传文件: {test_file_path}")
            
            # 发送请求
            response = requests.post(url, files=files, data=data, timeout=30)
            
            print(f"📊 响应状态码: {response.status_code}")
            print(f"📄 响应内容: {response.text}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 上传成功!")
                print(f"批次ID: {result.get('batch_id')}")
                print(f"上传文件数: {result.get('total_files')}")
                return True
            else:
                print(f"❌ 上传失败: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ 上传异常: {e}")
        return False

def test_batch_status(batch_id):
    """测试批次状态查询"""
    
    url = f"http://127.0.0.1:8000/api/v1/documents/batch/{batch_id}/status"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"📊 批次状态响应: {response.status_code}")
        print(f"📄 批次状态内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 批次状态查询成功!")
            print(f"批次状态: {result.get('batch_status')}")
            print(f"文件数量: {len(result.get('files', []))}")
            
            for file_info in result.get('files', []):
                print(f"  - {file_info.get('filename')}: {file_info.get('status')}")
            
            return True
        else:
            print(f"❌ 批次状态查询失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 批次状态查询异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试文档上传接口...")
    
    # 测试上传
    success = test_document_upload()
    
    if success:
        print("\n⏳ 等待3秒后查询状态...")
        import time
        time.sleep(3)
        
        # 这里需要从上传响应中获取batch_id，简化测试先跳过
        print("📝 批次状态查询需要batch_id，请手动测试")
    
    print("\n🎯 测试完成!")
