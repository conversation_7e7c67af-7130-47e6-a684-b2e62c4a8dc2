# 阿里云文档解析服务
import asyncio
from typing import Dict, Any, Optional
from src.config.settings import AlibabaCloudSettings

# 阿里云文档解析包
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.client import Client as CredClient


class AlibabaCloudService:
    def __init__(self, settings: AlibabaCloudSettings):
        """
        初始化阿里云文档解析服务

        Args:
            settings: 阿里云配置实例
        """
        self.settings = settings
        self.config = open_api_models.Config(
            type="access_key",
            access_key_id=settings.ACCESS_KEY_ID,
            access_key_secret=settings.ACCESS_KEY_SECRET,
            endpoint=settings.ENDPOINT
        )

        # 初始化client
        self.client = docmind_api20220711Client(self.config)
    
    async def submit_file(self, file_url: str, file_name: str) -> Dict[str, Any]:
        """
        异步提交文件到阿里云文档解析服务

        Args:
            file_url (str): 文件URL路径
            file_name (str): 文件名称，必须包含后缀

        Returns:
            Dict[str, Any]: 包含任务ID和状态的响应

        Raises:
            Exception: 当提交任务失败时抛出异常
        """

        # 创建请求参数
        request = docmind_api20220711_models.SubmitDocParserJobRequest(
            file_url=file_url,
            file_name=file_name,
            formula_enhancement=True,
            llm_enhancement=True,
        )

        try:
            # 使用 asyncio.to_thread 将同步调用转为异步
            response = await asyncio.to_thread(self.client.submit_doc_parser_job, request)

            # 解析响应
            if response and response.body:
                return {
                    "success": True,
                    "job_id": response.body.data.id,
                    "message": "文档解析任务提交成功"
                }
            else:
                raise Exception("阿里云服务返回空响应")

        except Exception as e:
            error_msg = str(e)
            print(f"阿里云文档解析提交失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "文档解析任务提交失败"
            }

    async def get_job_result(self, job_id: str, layout_num: int = 0, layout_step_size: int = 100) -> Dict[str, Any]:
        """
        获取文档解析任务结果

        Args:
            job_id (str): 任务ID
            layout_num (int): 查询起始块位置，默认0
            layout_step_size (int): 期望查询的Layout的Size大小，默认100

        Returns:
            Dict[str, Any]: 任务结果
        """

        request = docmind_api20220711_models.GetDocParserResultRequest(
            id=job_id,
            layout_num=layout_num,
            layout_step_size=layout_step_size
        )

        try:
            response = await asyncio.to_thread(self.client.get_doc_parser_result, request)

            if response and response.body and response.body.data:
                data = response.body.data

                # 处理layouts数据，转换为markdown
                markdown_content = ""

                # 检查数据类型并相应处理
                if isinstance(data, dict):
                    # 字典类型处理
                    layouts = data.get('layouts', [])
                    if layouts:
                        for layout in layouts:
                            if isinstance(layout, dict) and 'markdownContent' in layout:
                                content = layout['markdownContent']
                                if content:
                                    markdown_content += content + "\n"
                else:
                    # 对象类型处理
                    if hasattr(data, 'layouts') and data.layouts:
                        for layout in data.layouts:
                            if hasattr(layout, 'markdown_content') and layout.markdown_content:
                                markdown_content += layout.markdown_content + "\n"
                            elif hasattr(layout, 'markdownContent') and layout.markdownContent:
                                markdown_content += layout.markdownContent + "\n"

                return {
                    "success": True,
                    "result": markdown_content.strip(),
                    "message": "获取任务结果成功"
                }
            else:
                raise Exception("阿里云服务返回空响应")

        except Exception as e:
            error_msg = str(e)
            print(f"获取阿里云任务结果失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "获取任务结果失败"
            }

    async def check_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        检查文档解析任务状态

        Args:
            job_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """

        request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)

        try:
            # 使用 QueryDocParserStatus 接口检查状态
            response = await asyncio.to_thread(self.client.query_doc_parser_status, request)

            if response and response.body:
                # 检查响应结构
                if hasattr(response.body, 'data') and response.body.data:
                    data = response.body.data

                    # 获取状态
                    status = getattr(data, 'status', 'unknown')

                    return {
                        "success": True,
                        "status": status,
                        "is_completed": status.lower() in ["success", "fail"],
                        "is_success": status.lower() == "success",
                        "message": f"任务状态: {status}"
                    }
                else:
                    # 有时候状态可能直接在body中
                    if hasattr(response.body, 'status'):
                        status = response.body.status
                        return {
                            "success": True,
                            "status": status,
                            "is_completed": status.lower() in ["success", "fail"],
                            "is_success": status.lower() == "success",
                            "message": f"任务状态: {status}"
                        }
                    else:
                        raise Exception("阿里云状态查询返回空响应或格式不正确")
            else:
                raise Exception("阿里云状态查询返回空响应")

        except Exception as e:
            error_msg = str(e)
            print(f"检查阿里云任务状态失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "检查任务状态失败"
            }

    async def download_images_from_markdown(self, markdown_content: str, minio_service, original_filename: str) -> str:
        """
        从markdown内容中下载图片并替换为MinIO URL

        Args:
            markdown_content (str): 包含阿里云临时图片URL的markdown内容
            minio_service: MinIO服务实例

        Returns:
            str: 替换后的markdown内容
        """
        import re
        import httpx
        import uuid
        from urllib.parse import urlparse

        # 匹配markdown中的图片URL
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'

        async def replace_image_url(match):
            alt_text = match.group(1)
            img_url = match.group(2)

            try:
                # 下载图片
                async with httpx.AsyncClient() as client:
                    response = await client.get(img_url)
                    response.raise_for_status()

                    # 生成新的文件名，使用指定的路径约定
                    parsed_url = urlparse(img_url)
                    file_extension = parsed_url.path.split('.')[-1] if '.' in parsed_url.path else 'jpg'
                    # 从原始文件名中移除扩展名
                    base_filename = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                    new_filename = f"parsed_markdowns/{base_filename}_images/{uuid.uuid4()}.{file_extension}"

                    # 上传到MinIO
                    minio_url = await minio_service.upload_file(
                        bucket_name=self.settings.BUCKET_NAME or "zhanshu",
                        object_name=new_filename,
                        file_stream=response.content,
                        length=len(response.content),
                        content_type=f"image/{file_extension}"
                    )

                    return f"![{alt_text}]({minio_url})"

            except Exception as e:
                print(f"下载图片失败 {img_url}: {e}")
                # 如果下载失败，保留原URL
                return match.group(0)

        # 异步替换所有图片URL
        import asyncio
        matches = list(re.finditer(img_pattern, markdown_content))

        if not matches:
            return markdown_content

        # 并发处理所有图片
        tasks = [replace_image_url(match) for match in matches]
        replacements = await asyncio.gather(*tasks)

        # 替换内容
        result = markdown_content
        for match, replacement in zip(reversed(matches), reversed(replacements)):
            result = result[:match.start()] + replacement + result[match.end():]

        return result