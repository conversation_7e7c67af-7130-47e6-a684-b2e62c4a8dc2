#!/usr/bin/env python3
"""
简化的阿里云文档解析API测试
"""

import asyncio
from src.config.settings import AlibabaCloudSettings
from src.document_rag.ali_service import AlibabaCloudService
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models

async def test_aliyun_status():
    """测试阿里云状态查询"""
    
    print("🚀 开始测试阿里云状态查询...")
    
    # 初始化配置
    settings = AlibabaCloudSettings()
    service = AlibabaCloudService(settings)
    
    # 测试文件URL
    test_file_url = "http://119.3.237.14:9000/zhanshu/documents/MCHOSE%2020250415.pdf"
    test_file_name = "MCHOSE 20250415.pdf"
    
    try:
        # 1. 提交解析任务
        print(f"📤 提交解析任务: {test_file_name}")
        submit_result = await service.submit_file(test_file_url, test_file_name)
        
        if not submit_result["success"]:
            print(f"❌ 任务提交失败: {submit_result['error']}")
            return False
        
        job_id = submit_result["job_id"]
        print(f"✅ 任务提交成功，任务ID: {job_id}")
        
        # 2. 直接测试状态查询API
        print(f"🔍 测试状态查询API...")
        status_request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)
        
        status_response = await asyncio.to_thread(service.client.query_doc_parser_status, status_request)
        
        print(f"📊 状态响应: {type(status_response)}")
        if status_response:
            print(f"📊 Body: {type(status_response.body)}")
            if status_response.body:
                print(f"📊 Body属性: {dir(status_response.body)}")
                
                if hasattr(status_response.body, 'data'):
                    data = status_response.body.data
                    print(f"📊 Data: {type(data)}")
                    
                    if isinstance(data, dict):
                        print(f"📊 Data键: {list(data.keys())}")
                        status = data.get('status', 'unknown')
                        print(f"✅ 状态: {status}")
                    else:
                        print(f"📊 Data属性: {dir(data)}")
                        if hasattr(data, 'status'):
                            print(f"✅ 状态: {data.status}")
                        else:
                            print(f"❌ 没有找到status属性")
                else:
                    print(f"❌ 响应中没有data字段")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_aliyun_status())
    
    if success:
        print("\n🎉 测试完成!")
    else:
        print("\n💥 测试失败!")
